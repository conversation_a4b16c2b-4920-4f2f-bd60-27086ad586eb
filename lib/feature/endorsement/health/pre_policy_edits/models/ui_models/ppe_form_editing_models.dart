import 'package:acko_flutter/common/model/picker_model.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:intl/intl.dart';

class FormValues {
  PPEFormEditingDetails? oldValues;
  PPEFormEditingDetails? newValues;
  EndorsementFormValidatorConfig? config;
  int? totalEdits;
  bool isRenewalFlow;

  FormValues(
      {this.oldValues,
      this.newValues,
      this.totalEdits = 0,
      this.config,
      this.isRenewalFlow = false});

  List<PickerModel> getAvailableRelationList(
      EndorsementFormValidatorConfig config,
      {bool isASP = false}) {
    List<MemberDetails?>? currentMembers = [
      ...?newValues?.memberDetails,
      newValues?.proposerDetails,
    ];
    final relationshipCounts = <String, int>{};

    for (var member in currentMembers) {
      if (member != null &&
          member.relation != null &&
          member.isRemoved == false) {
        String relation = member.relation!;
        // HealthJourneyManagerUtils().sanitizeValue(member.relation!).toLowerCase();
        relationshipCounts[relation] = (relationshipCounts[relation] ?? 0) + 1;
      }
    }

    List<PickerModel> availableRelationships = [];

    for (PickerModel pickerModel in HealthJourneyManagerUtils()
        .getRelationList(includeSelf: isRenewalFlow)) {
      bool isAvailable = true;
      for (FamilyConstrains constraint in config.familyConstrains) {
        if (constraint.option?.equalsIgnoreCase(pickerModel.id) ?? false) {
          String relation = pickerModel.id;
          // HealthJourneyManagerUtils().sanitizeValue(pickerModel.id).toLowerCase();
          final currentCount = relationshipCounts[relation] ?? 0;
          if (currentCount >= (constraint.max ?? double.infinity)) {
            isAvailable = false;
          }
          break;
        }
      }
      if (isAvailable) {
        if (isASP &&
            (pickerModel.id == 'Parent' || pickerModel.id == 'Parent-in-Law')) {
          continue;
        }
        availableRelationships.add(pickerModel);
      }
    }

    return availableRelationships;
  }

  bool get isAsp =>
      newValues?.policyDetails?.packageName.containsIgnoreCase("arogya") ??
      false;

  int getMaxMemberLimit() {
    int maxMemberLimit = 10;
    if (newValues?.policyDetails?.packageName.containsIgnoreCase("arogya") ??
        false) {
      maxMemberLimit = 6;
    }
    return maxMemberLimit;
  }

  setConfig(EndorsementFormValidatorConfig validationConfig) {
    config = validationConfig;
  }

  void populateFormValuesFromPrePolicyEditsResponse(Values? values,
      List<PackagePlan>? packagePlans, Map<String, String>? deductibles,
      {required bool isOld, bool isRenewal = false, String? paymentDate}) {
    InsuredContainer? insuredUsers = values?.insuredContainer;
    UsersContainer? proposerDetails = values?.usersContainer;

    List<MemberDetails?>? memberDetailsList = [];

    String? proposerId = proposerDetails?.usersMap.values.firstOrNull?.userId;

    MemberDetails? proposerDetailsMember;

    // STEP 1: Always create proposer details from users container first
    // This ensures proposer is ALWAYS populated regardless of self-exclusion status
    if (proposerDetails?.usersMap.values.firstOrNull != null) {
      bool wasSelfExcluded = insuredUsers?.insuredMap.values
              .where((insured) =>
                  insured.parameters?.parameterMap['user_id']?.value ==
                  proposerDetails?.usersMap.values.firstOrNull?.userId)
              .isEmpty ??
          true;

      // Preserve existing edit state when rebuilding from API response
      MemberDetails? existingProposer =
          isOld ? null : this.newValues?.proposerDetails;

      // CRITICAL FIX: Preserve self-exclusion state absolutely
      // Once a proposer is self-excluded, they stay self-excluded until explicitly added back
      bool preserveIsSelfExcluded = false;
      bool preserveWasSelfExcluded = false;

      if (existingProposer != null) {
        // If we have existing proposer state, preserve it completely
        preserveIsSelfExcluded = existingProposer.isSelfExcluded;
        preserveWasSelfExcluded = existingProposer.wasSelfExcluded;
      } else {
        // FIXED: For API rebuilds, be more conservative about marking proposer as self-excluded
        // Only mark as self-excluded if this is the initial load (oldValues) or if explicitly set
        if (isOld) {
          // For oldValues, use API-derived state
          preserveIsSelfExcluded = wasSelfExcluded;
          preserveWasSelfExcluded = wasSelfExcluded;
        } else {
          // For newValues, use API-derived state to reflect current reality
          // If API shows proposer is not in insured container, they are self-excluded
          preserveIsSelfExcluded = wasSelfExcluded;
          preserveWasSelfExcluded = wasSelfExcluded;
        }
      }

      proposerDetailsMember = MemberDetails.fromMap(
        Map<String, dynamic>.from(proposerDetails!
            .usersMap.values.firstOrNull!.parameters.parameterMap),
        true,
        insuredNumber: proposerDetails.usersMap.values.firstOrNull?.userId,
        isSelfExcluded: preserveIsSelfExcluded,
        wasSelfExcluded: preserveWasSelfExcluded,
      );

      // CRITICAL: Ensure userId matches the usersMap key for backend updates
      String? actualUserId =
          proposerDetails.usersMap.values.firstOrNull?.userId;
      if (actualUserId != null &&
          proposerDetailsMember.userId != actualUserId) {
        proposerDetailsMember.userId = actualUserId;
      }

      // Preserve other edit-related flags from existing state
      if (existingProposer != null && !isOld) {
        proposerDetailsMember.isRemoved = existingProposer.isRemoved;
        proposerDetailsMember.isNewlyAdded = existingProposer.isNewlyAdded;
      }

      // STEP 2: If proposer exists in insured container, merge the data
      Insured? proposerInsured;
      try {
        proposerInsured = insuredUsers?.insuredMap.values.firstWhere(
          (insured) => insured.parameters!.parameterMap['user_id']?.value
              .equalsIgnoreCase(proposerId ?? ""),
        );
      } catch (e) {
        proposerInsured = null;
      }

      if (proposerInsured != null && !isRenewal) {
        // Merge insured data with users data for non-self-excluded proposers
        final mergedUsersMap = HealthJourneyManagerUtils().mergeParameterMaps(
          proposerDetails.usersMap.values.firstOrNull?.parameters.parameterMap,
          proposerInsured.parameters!.parameterMap,
        );

        // Update proposer with merged data while preserving edit state
        final mergedProposer = MemberDetails.fromMap(mergedUsersMap, true,
            insuredNumber: proposerInsured.insuredNumber);

        // Preserve edit state from existing proposer
        mergedProposer.isSelfExcluded = preserveIsSelfExcluded;
        mergedProposer.wasSelfExcluded = preserveWasSelfExcluded;
        mergedProposer.isRemoved = proposerDetailsMember.isRemoved;
        mergedProposer.isNewlyAdded = proposerDetailsMember.isNewlyAdded;

        // CRITICAL: Ensure userId matches the usersMap key for backend updates
        String? actualUserId =
            proposerDetails.usersMap.values.firstOrNull?.userId;
        if (actualUserId != null && mergedProposer.userId != actualUserId) {
          mergedProposer.userId = actualUserId;
        }

        proposerDetailsMember = mergedProposer;
      }
    }

    // STEP 3: Process regular members (excluding proposer)
    insuredUsers?.insuredMap.values.forEach((insured) {
      String? userId = insured.parameters!.parameterMap['user_id']?.value;
      final insuredNumber = insured.insuredNumber;

      // Skip proposer - they're already handled above in proposerDetails
      // Use multiple checks to ensure proposer is never added to memberDetails
      bool isProposerMember = userId.equalsIgnoreCase(proposerId ?? "") ||
          insuredNumber.equalsIgnoreCase(proposerId ?? "") ||
          (proposerDetailsMember != null &&
              (userId.equalsIgnoreCase(proposerDetailsMember.userId ?? "") ||
                  insuredNumber.equalsIgnoreCase(
                      proposerDetailsMember.insuredId ?? "")));

      if (!isProposerMember) {
        if (insured.parameters!.parameterMap.isNotNullOrEmpty) {
          MemberDetails memberDetails = MemberDetails.fromMap(
              insured.parameters!.parameterMap, false,
              insuredNumber: insuredNumber);

          // Preserve existing edit state for regular members when rebuilding from API response
          if (!isOld && this.newValues?.memberDetails != null) {
            MemberDetails? existingMember =
                this.newValues?.memberDetails?.firstWhere(
                      (m) => m?.insuredId == memberDetails.insuredId,
                      orElse: () => null,
                    );
            if (existingMember != null) {
              memberDetails.isRemoved = existingMember.isRemoved;
              memberDetails.isNewlyAdded = existingMember.isNewlyAdded;
            }
          }

          memberDetailsList.add(memberDetails);
        }
      }
    });

    PortingDetails? portingDetails;
    PolicyDetails? policyDetails;
    if (!isRenewal) {
      if (values?.portingDetails?.currentPolicyExpiryDate.isNotNullOrEmpty ??
          false) {
        portingDetails = PortingDetails.fromPolicyPortingDetails(
            values?.portingDetails, paymentDate);
      }
      policyDetails = PolicyDetails.fromCurrentPlanToPolicyDetails(
          values?.currentPlan, packagePlans, deductibles);
    }

    // CRITICAL: For newValues, preserve removed members that are not in API response
    // BUT exclude self-excluded proposers - they should only appear in proposer details section
    if (!isOld && this.newValues?.memberDetails != null) {
      for (var existingMember in this.newValues!.memberDetails!) {
        if (existingMember != null && existingMember.isRemoved) {
          // CRITICAL FIX: Skip self-excluded proposers - they belong in proposer details only
          if (existingMember.isProposer && existingMember.isSelfExcluded) {
            continue; // Don't add self-excluded proposer to member details
          }

          // Check if this removed member is already in the new list
          bool alreadyExists = memberDetailsList
              .any((m) => m?.insuredId == existingMember.insuredId);
          if (!alreadyExists) {
            // Add the removed member back to preserve removal state
            memberDetailsList.add(existingMember);
          }
        }
      }
    }

    // Create the formDetails object to store in old or new values
    PPEFormEditingDetails formDetails = PPEFormEditingDetails(
      portingDetails: isRenewal ? null : portingDetails,
      policyDetails: isRenewal ? null : policyDetails,
      memberDetails: memberDetailsList,
      proposerDetails: proposerDetailsMember,
    );

    // Update oldValues or newValues based on the isOld flag
    if (isOld) {
      this.oldValues = formDetails;
    } else {
      this.newValues = formDetails;
    }
  }

  void updateMemberStatus() {
    final oldMembers = oldValues?.memberDetails ?? [];
    final newMembers = newValues?.memberDetails ?? [];

    final oldIds = oldMembers.map((m) => m?.insuredId).toSet();
    final newIds = newMembers.map((m) => m?.insuredId).toSet();

    // Mark newly added members in newValues
    for (var member in newMembers) {
      if (member != null) {
        if (!oldIds.contains(member.insuredId)) {
          member.isNewlyAdded = true;
          // Don't reset isRemoved for newly added members - preserve existing state
          if (!member.isRemoved) {
            member.isRemoved = false;
          }
        } else {
          member.isNewlyAdded = false;
          // CRITICAL: Don't reset isRemoved for existing members - preserve removal state
          // member.isRemoved = false; // ❌ This was resetting removal state
        }
      }
    }

    /// Mark removed members in newValues
    for (var member in oldMembers) {
      if (member != null && !newIds.contains(member.insuredId)) {
        member.isRemoved = true;
        member.isNewlyAdded = false;

        /// FIXED: Only add if not already present to avoid duplication
        bool alreadyExists =
            newMembers.any((m) => m?.insuredId == member.insuredId);
        if (!alreadyExists) {
          newMembers.add(member);
        }
      }
    }

    /// CRITICAL: Remove any duplicates based on insuredId to ensure clean list
    Map<String, MemberDetails?> uniqueMembers = {};
    for (var member in newMembers) {
      if (member?.insuredId != null) {
        uniqueMembers[member!.insuredId!] = member;
      }
    }

    /// Update the newValues with the deduplicated list of members
    newValues?.memberDetails = uniqueMembers.values.toList();
  }

  void sortNewByOldValues() {
    /// Create a map of insuredId to index from list A (oldValues)
    final insuredIdToIndexMap = <String, int>{};

    /// Ensure oldValues and memberDetails are not null
    if (oldValues?.memberDetails != null) {
      for (int i = 0; i < oldValues!.memberDetails!.length; i++) {
        final insuredId = oldValues!.memberDetails![i]?.insuredId;

        /// Add to the map only if insuredId is not null
        if (insuredId != null) {
          insuredIdToIndexMap[insuredId] = i;
        }
      }
    }

    /// Ensure newValues and memberDetails are not null before sorting
    if (newValues?.memberDetails != null) {
      newValues!.memberDetails!.sort((a, b) {
        /// Handle cases where insuredId is null in newValues
        final insuredIdA = a?.insuredId;
        final insuredIdB = b?.insuredId;

        final indexA =
            insuredIdA != null ? insuredIdToIndexMap[insuredIdA] : null;
        final indexB =
            insuredIdB != null ? insuredIdToIndexMap[insuredIdB] : null;

        /// Handle cases where both ids are null or not found in the old list
        if (indexA == null && indexB == null) {
          /// maintains original order
          return 0;
        } else if (indexA == null) {
          /// put elements not in list A at the end
          return 1;
        } else if (indexB == null) {
          /// put elements not in list A at the end
          return -1;
        } else {
          /// sort based on index in old list A
          return indexA.compareTo(indexB);
        }
      });
    }
  }

  int _calculateDistinctEntries(
      List<MemberDetails?>? oldList, List<MemberDetails?>? newList) {
    /// CRITICAL FIX: Exclude proposer from member addition/removal count
    /// Proposer changes are counted separately in proposer comparison

    // Filter out proposers from both lists
    final oldNonProposers =
        oldList?.where((member) => member?.isProposer != true).toList() ?? [];
    final newNonProposers =
        newList?.where((member) => member?.isProposer != true).toList() ?? [];

    /// Convert filtered lists to sets of insuredIds
    final oldIds = oldNonProposers.map((member) => member?.insuredId).toSet();
    final newIds = newNonProposers.map((member) => member?.insuredId).toSet();

    /// Calculate distinct insuredIds between both sets
    final distinctOld = oldIds.difference(newIds);
    final distinctNew = newIds.difference(oldIds);

    /// Return the total number of distinct entries (excluding proposer)
    return distinctOld.length + distinctNew.length;
  }

  bool get areAllEditsValid {
    if (newValues == null) {
      return false;
    }

    /// Check PortingDetails
    // if (newValues?.policyDetails != null && newValues!.portingDetails?.portingDate == null) {
    //   return false;
    // }

    /// Check PolicyDetails
    // if ((newValues!.policyDetails?.packageName.isNullOrEmpty ?? false) ||
    //     (newValues!.policyDetails?.sumInsured?.name.isNullOrEmpty ?? false) ||
    //     (newValues!.policyDetails?.deductible?.name.isNullOrEmpty ?? false)) {
    //   return false;
    // }

    /// Check ProposerDetails

    List<String> proposerDisabledField = [];

    if (oldValues?.proposerDetails != null &&
        oldValues!.proposerDetails!.height == null) {
      proposerDisabledField.add('height');
    }
    if (oldValues?.proposerDetails != null &&
        oldValues!.proposerDetails!.weight == null) {
      proposerDisabledField.add('weight');
    }

    if (_areMemberDetailsInvalid(
        newValues!.proposerDetails, proposerDisabledField)) {
      if (isRenewalFlow && newValues!.proposerDetails == null) {
        return true;
      }
      return false;
    }

    /// Check MemberDetails
    if (newValues!.memberDetails != null) {
      for (var newMember in newValues!.memberDetails!) {
        /// to ignore unwanted fields...
        List<String> memberDisabledField = [];

        String? insuredId = newMember?.insuredId;

        if (insuredId.isNotNullOrEmpty) {
          MemberDetails? oldMember = _getOldValueForInsuredId(insuredId!);
          if (oldMember != null && oldMember.height == null) {
            memberDisabledField.add('height');
          }
          if (oldMember != null && oldMember.weight == null) {
            memberDisabledField.add('weight');
          }
        }

        if (_areMemberDetailsInvalid(newMember, memberDisabledField)) {
          return false;
        }
      }
    }

    /// If all checks pass
    return true;
  }

  MemberDetails? _getOldValueForInsuredId(String insuredId) {
    return oldValues?.memberDetails?.firstWhereOrNullIterable(
        (element) => element?.insuredId == insuredId);
  }

  bool _areMemberDetailsInvalid(
      MemberDetails? memberDetails, List<String> disabledFields) {
    if (memberDetails == null) {
      return true;
    }
    if (memberDetails.isSelfExcluded) return false;

    if (memberDetails.name.isNullOrEmpty ||
        memberDetails.gender.isNullOrEmpty ||
        memberDetails.dateOfBirth.isNullOrEmpty ||
        memberDetails.relation.isNullOrEmpty) {
      return true;
    }
    if (RegExp(r'[^a-zA-Z\s]').hasMatch(memberDetails.name!)) return true;

    if (!disabledFields.contains('height') &&
        memberDetails.height.isNullOrEmpty) {
      return true;
    }

    if (!disabledFields.contains('weight') &&
        memberDetails.weight.isNullOrEmpty) {
      return true;
    }

    if (!disabledFields.contains('height') &&
        !disabledFields.contains('weight')) {
      String ackoHeight = HealthJourneyManagerUtils()
          .fromInchFeetToAckoHeight(memberDetails.height!);
      String ackoWeight = memberDetails.weight!;

      int heightValue = int.parse(ackoHeight);
      int maxHeightValue = int.parse(
          HealthJourneyManagerUtils().fromInchFeetToAckoHeight("9'11\""));
      int weightValue = int.parse(ackoWeight);

      if (weightValue < 0 || weightValue >= 200) {
        return true;
      }

      if (heightValue < 0 || heightValue >= maxHeightValue) {
        return true;
      }
      if (isDateOfBirthInvalid(memberDetails)) {
        return true;
      }
    }

    return false;
  }

  bool isDateOfBirthInvalid(MemberDetails memberDetails) {
    if (memberDetails.dateOfBirth == null || memberDetails.relation == null) {
      return true;
    }

    DateTime today = DateTime.now();
    DateTime? dateOfBirth;

    try {
      dateOfBirth = DateFormat('dd-MM-yyyy').parse(memberDetails.dateOfBirth!);
    } catch (e) {
      return true; // Return true if date parsing fails
    }

    FamilyConstrains? constraint;
    for (var c in config!.familyConstrains) {
      if (c.option.equalsIgnoreCase(memberDetails.relation!)) {
        constraint = c;
        break;
      }
    }

    if (constraint != null && constraint.age != null) {
      DateTime minDate;
      DateTime maxDate;

      switch (constraint.age!.min!.qualifier) {
        case 'calendar':
          minDate = DateTime(
            today.year - (constraint.age!.min!.value?.y ?? 0),
            today.month - (constraint.age!.min!.value?.m ?? 0),
            today.day - (constraint.age!.min!.value?.d ?? 0),
          );
          break;

        case 'day':
          minDate = today
              .subtract(Duration(days: constraint.age!.min!.value?.days ?? 0));
          break;

        default:
          minDate =
              DateTime(today.year - 100, today.month, today.day); // Fallback
          break;
      }

      switch (constraint.age!.max!.qualifier) {
        case 'calendar':
          maxDate = DateTime(
            today.year - (constraint.age!.max!.value?.y ?? 0),
            today.month - (constraint.age!.max!.value?.m ?? 0),
            today.day - (constraint.age!.max!.value?.d ?? 0),
          );
          break;

        case 'day':
          maxDate = today
              .subtract(Duration(days: constraint.age!.max!.value?.days ?? 0));
          break;

        default:
          maxDate =
              DateTime(today.year - 18, today.month, today.day); // Fallback
          break;
      }

      if (dateOfBirth.isAfter(minDate) || dateOfBirth.isBefore(maxDate)) {
        return true;
      }
    } else {
      return true;

      /// Return true if no constraints found
    }

    return false;
  }

  int get calculateTotalEdits {
    int totalEdits = 0;

    totalEdits = _calculateDistinctEntries(
        oldValues?.memberDetails, newValues?.memberDetails);

    if (oldValues != null && newValues != null) {
      /// Compare PortingDetails
      if (oldValues!.portingDetails?.portingDate !=
          newValues!.portingDetails?.portingDate) {
        totalEdits++;
      }

      /// Compare PolicyDetails
      if (oldValues!.policyDetails?.packageName !=
          newValues!.policyDetails?.packageName) {
        totalEdits++;
      }

      if ((oldValues!.policyDetails != null &&
              newValues!.policyDetails != null) &&
          (oldValues!.policyDetails!.sumInsured != null) &&
          (newValues!.policyDetails!.sumInsured != null)) {
        if ((oldValues!.policyDetails!.sumInsured?.name.isNotNullOrEmpty ??
                false) &&
            (newValues!.policyDetails!.sumInsured?.name.isNotNullOrEmpty ??
                false)) {
          if (oldValues!.policyDetails!.sumInsured!.name.equalsIgnoreCase(
                  newValues!.policyDetails!.sumInsured?.name) ==
              false) {
            totalEdits++;
          }
        }
      }

      if ((oldValues!.policyDetails != null &&
              newValues!.policyDetails != null) &&
          (oldValues!.policyDetails!.deductible != null) &&
          (newValues!.policyDetails!.deductible != null)) {
        if ((oldValues!.policyDetails!.deductible?.name.isNotNullOrEmpty ??
                false) &&
            (newValues!.policyDetails!.deductible?.name.isNotNullOrEmpty ??
                false)) {
          if (oldValues!.policyDetails!.deductible!.name.equalsIgnoreCase(
                  newValues!.policyDetails!.deductible?.name) ==
              false) {
            totalEdits++;
          }
        }
      }

      /// Compare ProposerDetails
      int proposerEdits = _compareMemberDetails(
          oldValues!.proposerDetails, newValues!.proposerDetails);
      totalEdits += proposerEdits;

      /// Compare MemberDetails
      if (oldValues!.memberDetails != null &&
          newValues!.memberDetails != null) {
        for (var oldMember in oldValues!.memberDetails!) {
          // Find the corresponding new member with the same insuredId
          var newMember = newValues!.memberDetails!.firstWhere(
            (newMember) => newMember?.insuredId == oldMember?.insuredId,
            orElse: () => null,
          );

          if (newMember != null) {
            totalEdits += _compareMemberDetails(oldMember, newMember);
          }
        }
      }
    }

    return totalEdits;
  }

  int _compareMemberDetails(
      MemberDetails? oldMember, MemberDetails? newMember) {
    int edits = 0;

    if (oldMember != null && newMember != null) {
      // CRITICAL FIX: Count proposer self-exclusion correctly
      // If proposer is currently self-excluded but wasn't before, count as 1 edit
      bool isProposerSelfExclusion =
          newMember.isSelfExcluded && !oldMember.isSelfExcluded;

      // Count regular member removal (non-proposer)
      bool isRegularRemoval = newMember.isRemoved && !newMember.isSelfExcluded;

      if (isProposerSelfExclusion || isRegularRemoval) {
        edits++;
      }

      // FIXED: Always count field changes, regardless of self-exclusion status
      // Self-excluded proposers can still edit their details (name, DOB, etc.)
      if (oldMember.userId.notEqualsIgnoreCase(newMember.userId)) edits++;
      if (oldMember.insuredId != newMember.insuredId) edits++;
      if (oldMember.name != newMember.name) edits++;
      if (oldMember.gender.notEqualsIgnoreCase(newMember.gender)) edits++;
      if (oldMember.dateOfBirth != newMember.dateOfBirth) edits++;
      if (oldMember.height != newMember.height) edits++;
      if (oldMember.weight != newMember.weight) edits++;
      if (oldMember.pinCode != newMember.pinCode) edits++;
      if (oldMember.mobileNumber != newMember.mobileNumber) edits++;
      if (oldMember.email != newMember.email) edits++;
      if (oldMember.relation.notEqualsIgnoreCase(newMember.relation)) edits++;

      /// oldMember.isRemoved !=
    }

    return edits;
  }
}

class PPEFormEditingDetails {
  PortingDetails? portingDetails;
  PolicyDetails? policyDetails;
  MemberDetails? proposerDetails;
  List<MemberDetails?>? memberDetails;
  PolicyPremium? policyPremium;

  PPEFormEditingDetails(
      {this.portingDetails,
      this.policyDetails,
      this.memberDetails,
      this.proposerDetails,
      this.policyPremium});

  handleRemovedMember(Map<String, Insured>? insuredMap) {}
}

class MemberDetails {
  String? userId;
  String? insuredId;
  String? name;
  String? gender;
  String? dateOfBirth;
  String? height;
  String? weight;
  String? pinCode;
  String? mobileNumber;
  String? email;
  String? relation;
  String? uhid;
  bool isProposer;
  bool isRemoved;
  bool isNewlyAdded;
  bool isSelfExcluded;
  bool wasSelfExcluded;

  /// more values added
  String? relationship;
  dynamic age;
  String? bmi;
  String? insuredNumber;
  String? maritalStatus;
  String? role;

  MemberDetails({
    required this.userId,
    required this.insuredId,
    required this.name,
    required this.gender,
    required this.dateOfBirth,
    required this.height,
    required this.weight,
    required this.pinCode,
    required this.mobileNumber,
    required this.email,
    required this.relation,
    this.isProposer = false,
    this.isRemoved = false,
    this.isNewlyAdded = false,
    this.uhid,
    this.relationship,
    this.age,
    this.bmi,
    this.maritalStatus,
    this.role,
    this.insuredNumber,
    this.isSelfExcluded = false,
    this.wasSelfExcluded = false,
  });

  factory MemberDetails.fromMap(Map<String, dynamic> map, bool isProposer,
      {String? insuredNumber,
      bool isSelfExcluded = false,
      bool wasSelfExcluded = false}) {
    // todo: verify made changes here: from (map['user_id']?.value ?? '') as String ---> (map['user_id']?.value) as String?
    return MemberDetails(
      userId: (map['user_id']?.value) as String?,
      insuredId: (map['id']?.value) as String?,
      name: (map['name']?.value) as String?,
      gender: (map['gender']?.value) as String?,
      dateOfBirth: (map['dob']?.value) as String?,
      height: map['height'] != null
          ? HealthJourneyManagerUtils()
              .fromAckoHeightToInchFeet((map['height']?.value).toString())
          : null,
      // weight: map['weight'] != null
      //     ? (map['weight']?.value?.toInt() ?? 0).toString()
      //     : null,
      weight: map['weight'] != null
          ? (map['weight']?.value is String
              ? map['weight']?.value
              : (map['weight']?.value?.toInt() ?? 0).toString())
          : null,
      pinCode: (map['pincode']?.value) as String?,
      mobileNumber: (map['phone']?.value) as String?,
      email: (map['email']?.value) as String?,
      relation: (map['relation']?.value) as String?,
      isProposer: isProposer,
      uhid: (map['UHID']?.value) as String?,
      relationship: (map['relationship']?.value) as String?,
      // age: map['age']?.value is int? ? map['age']?.value as int? : (int.tryParse(map['age']?.value)) as int?,
      age: map['age']
          ?.value, // ? map['age']?.value as int? : (int.tryParse(map['age']?.value)) as int?,
      bmi: (map['bmi']?.value) as String?,
      maritalStatus: (map['marital_status']?.value) as String?,
      role: (map['role']?.value) as String?,
      insuredNumber: insuredNumber,
      isSelfExcluded: isSelfExcluded,
      wasSelfExcluded: wasSelfExcluded,
    );
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};

    if (userId != null) {
      data['user_id'] = userId;
    }
    if (insuredId != null) {
      data['id'] = insuredId;
    }
    if (name != null) {
      data['name'] = name;
    }
    if (gender != null) {
      data['gender'] = gender;
    }
    if (relation != null) {
      data['relation'] = relation;
    }
    if (dateOfBirth != null) {
      data['dob'] = dateOfBirth;
    }
    if (height != null) {
      data['height'] = height;
    }
    if (weight != null) {
      data['weight'] = weight;
    }
    if (pinCode != null) {
      data['pincode'] = pinCode;
    }
    if (mobileNumber != null) {
      data['phone'] = mobileNumber;
    }
    if (email != null) {
      data['email'] = email;
    }
    if (uhid != null) {
      data['UHID'] = uhid;
    }
    if (relationship != null) {
      data['relationship'] = relationship;
    }
    if (age != null) {
      data['age'] = age;
    }
    if (bmi != null) {
      data['bmi'] = bmi;
    }
    if (insuredNumber != null) {
      data['insuredNumber'] = insuredNumber;
    }
    if (maritalStatus != null) {
      data['marital_status'] = maritalStatus;
    }
    if (role != null) {
      data['role'] = role;
    }

    return data;
  }

  // Map<String, String?> toMap() {
  //   return {
  //     'user_id': userId != null ? userId : null,
  //     'id': insuredId != null ? insuredId : null,
  //     'name': name != null ? name : null,
  //     'gender': gender != null ? gender : null,
  //     'relation': relation != null ? relation : null,
  //     'dob': dateOfBirth != null ? dateOfBirth : null,
  //     'height': height != null ? height : null,
  //     'weight': weight != null ? weight : null,
  //     'pincode': pinCode != null ? pinCode : null,
  //     'phone': mobileNumber != null ? mobileNumber : null,
  //     'email': email != null ? email : null,
  //   };
  // }

  MemberDetails copyWith({
    String? userId,
    String? insuredId,
    String? name,
    String? gender,
    String? dateOfBirth,
    String? height,
    String? weight,
    String? pinCode,
    String? mobileNumber,
    String? email,
    String? relation,
    bool? isProposer,
    String? uhid,
    bool? isRemoved,
    bool? isNewlyAdded,
    bool? isSelfExcluded,
    bool? wasSelfExcluded,
  }) {
    return MemberDetails(
      userId: userId ?? this.userId,
      insuredId: insuredId ?? this.insuredId,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      pinCode: pinCode ?? this.pinCode,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      email: email ?? this.email,
      relation: relation ?? this.relation,
      isProposer: isProposer ?? this.isProposer,
      uhid: uhid ?? this.uhid,
      // CRITICAL: Preserve all state flags
      isRemoved: isRemoved ?? this.isRemoved,
      isNewlyAdded: isNewlyAdded ?? this.isNewlyAdded,
      isSelfExcluded: isSelfExcluded ?? this.isSelfExcluded,
      wasSelfExcluded: wasSelfExcluded ?? this.wasSelfExcluded,
      // Preserve other fields
      relationship: this.relationship,
      age: this.age,
      bmi: this.bmi,
      maritalStatus: this.maritalStatus,
      role: this.role,
      insuredNumber: this.insuredNumber,
    );
  }
}

extension on MemberDetails {
  void updateField(String key, dynamic newValue) {
    switch (key) {
      case 'user_id':
        userId = newValue;
        break;
      case 'id':
        insuredId = newValue;
        break;
      case 'name':
        name = newValue;
        break;
      case 'gender':
        gender = newValue;
        break;
      case 'relation':
        gender = newValue;
        break;
      case 'dateOfBirth':
        dateOfBirth = newValue;
        break;
      case 'height':
        height = newValue;
        break;
      case 'weight':
        weight = newValue;
        break;
      case 'pinCode':
        pinCode = newValue;
        break;
      case 'mobileNumber':
        mobileNumber = newValue;
        break;
      case 'email':
        email = newValue;
        break;
      case 'UHID':
        uhid = newValue;
        break;
      default:
        break;
    }
  }
}

class PolicyDetails {
  String? packageId;
  String? packageName;
  String? packageType;
  String? productPlanType;
  String? productId;
  PickerModel? sumInsured;
  PickerModel? deductible;

  PackagePlan? packagePlanDetails;
  Map<String, String>? deductiblesList;

  PolicyDetails({
    this.packageId,
    this.packageName,
    this.packageType,
    this.productPlanType,
    this.productId,
    this.sumInsured,
    this.deductible,
    this.packagePlanDetails,
    this.deductiblesList,
  });

  Map<String, dynamic> toMap() {
    return {
      'packageId': packageId != null ? packageId : "",
      'packageName': packageName != null ? packageName : "",
      'packageType': packageType != null ? packageType : "",
      'productPlanType': productPlanType != null ? productPlanType : "",
      'productId': productId != null ? productId : "",
      'sumInsured': sumInsured,
      'deductible': deductible,
    };
  }

  String get formattedPackageName => packageName.isNotNullOrEmpty
      ? (packageName!.containsIgnoreCase('top-up')
          ? packageName!
          : "$packageName Health Plan")
      : '';
  String get formattedOverviewHeaderTitle => packageName.isNotNullOrEmpty
      ? (packageName!.containsIgnoreCase('top-up')
          ? packageName!
          : "$packageName Health policy")
      : '';

  factory PolicyDetails.fromCurrentPlanToPolicyDetails(CurrentPlan? currentPlan,
      List<PackagePlan>? packagePlanList, Map<String, String>? deductibleList) {
    PickerModel? siValues;
    PickerModel? deductibleValues;

    if (currentPlan != null && currentPlan.sumInsured != null) {
      siValues = PickerModel(
          id: currentPlan.sumInsured.toString(),
          name: HealthJourneyManagerUtils()
              .formatCurrencyWithUnits(currentPlan.sumInsured.toString()));
    }
    if (currentPlan?.deductible != null) {
      deductibleValues = PickerModel(
          id: currentPlan?.deductible ?? '',
          name: HealthJourneyManagerUtils()
              .formatCurrencyWithUnits(currentPlan?.deductible));
    }

    return PolicyDetails(
        packageId: currentPlan?.packageId,
        packageName: currentPlan?.packageName,
        packageType: currentPlan?.packageType,
        productPlanType: currentPlan?.productPlanType,
        productId: currentPlan?.productId,
        sumInsured: siValues,
        deductible: deductibleValues,
        deductiblesList: deductibleList,
        packagePlanDetails: packagePlanList.isNotNullOrEmpty
            ? () {
                for (var element in packagePlanList!) {
                  if (element.packageId == currentPlan?.packageId) {
                    return element;
                  }
                }
                return null;
              }()
            : null);
  }

  List<PickerModel> deductiblesToPickerModel(
      dynamic oldDeductibleValue, dynamic newDeductibleValue) {
    int oldDeductibleVal =
        int.tryParse(oldDeductibleValue?.toString() ?? '0') ?? 0;
    int newDeductibleVal =
        int.tryParse(newDeductibleValue?.toString() ?? '0') ?? 0;

    if (deductiblesList == null || deductiblesList!.isEmpty) {
      return [];
    }

    List<PickerModel> pickerModels = [];

    /// filtering logic:
    /// 1. Only include values less than or equal to the old deductible.
    /// 2. Exclude the new deductible if it's equal to the current key.
    /// 3. If old and new deductibles are the same, also filter out the new deductible.

    deductiblesList!.forEach((key, value) {
      int keyVal = int.parse(key);
      if (keyVal >= oldDeductibleVal) {
        pickerModels.add(PickerModel(name: "₹$value", id: key));
      }
    });

    /// Sort the resulting picker models by their integer value
    pickerModels.sort((a, b) => int.parse(a.id!).compareTo(int.parse(b.id!)));

    return pickerModels;
  }

  List<PickerModel> sumInsuredsToPickerModel(
      dynamic oldSumInsuredValue, dynamic newSumInsuredValue) {
    int oldSumInsuredVal =
        int.tryParse(oldSumInsuredValue?.toString() ?? '0') ?? 0;
    int newSumInsuredVal =
        int.tryParse(newSumInsuredValue?.toString() ?? '0') ?? 0;

    if (packagePlanDetails?.sumInsureds == null ||
        packagePlanDetails!.sumInsureds!.isEmpty) {
      return [];
    }

    List<PickerModel> pickerModels = [];

    packagePlanDetails!.sumInsureds!.forEach((element) {
      if (element.value != null) {
        int currentValue = element.value!;
        if (currentValue >= oldSumInsuredVal) {
          pickerModels.add(
              PickerModel(name: element.displayValue ?? '', id: element.value));
        }
      }
    });

    return pickerModels;
  }
}

class PortingDetails {
  String? portingDate;
  String? paymentDate;

  PortingDetails({this.portingDate, this.paymentDate});

  factory PortingDetails.fromPolicyPortingDetails(
      PolicyPortingDetails? policyPortingDetails, String? paymentDate) {
    String? dateString = policyPortingDetails?.currentPolicyExpiryDate;
    DateTime? parsedDate =
        dateString != null ? DateTime.tryParse(dateString) : null;
    String? formattedDate =
        parsedDate != null ? DateFormat('dd-MM-yyyy').format(parsedDate) : null;

    DateTime? parsedPaymentDate =
        paymentDate != null ? DateTime.tryParse(paymentDate) : null;
    String? formattedPaymentDate = parsedPaymentDate != null
        ? DateFormat('dd-MM-yyyy').format(parsedPaymentDate)
        : null;

    return PortingDetails(
      portingDate: formattedDate,
      paymentDate: formattedPaymentDate,
    );
  }

  // Map<String, String?> toMap() {
  //
  //   DateTime? parsedDate = portingDate != null ? DateFormat('dd-MM-yyyy').parse(portingDate!) : null;
  //   String? reformattedDate = parsedDate != null ? DateFormat('yyyy-MM-dd').format(parsedDate) : null;
  //   return {
  //     'portingDate': reformattedDate?.isNotEmpty == true ? reformattedDate : null,
  //   };
  // }

  String? getPortingDate() {
    DateTime? parsedDate = portingDate != null
        ? DateFormat('dd-MM-yyyy').parse(portingDate!)
        : null;
    String? reformattedDate =
        parsedDate != null ? DateFormat('yyyy-MM-dd').format(parsedDate) : null;
    return reformattedDate;
  }
}

class PolicyPremium {
  final String? paymentFrequency;

  /// payment_frequency
  final double? toBePaid;

  /// adhoc_payment
  final double? paidAmount;

  /// paid_amount
  final double? newAmount;

  /// new_installment
  final double? previousInstallment;

  /// previous_installment
  final double? newInstallment;

  /// new_installment

  PolicyPremium({
    this.paymentFrequency,
    this.toBePaid,
    this.paidAmount,
    this.newAmount,
    this.previousInstallment,
    this.newInstallment,
  });

  factory PolicyPremium.fromDeltaPremium(DeltaPremium? deltaPremium) {
    return PolicyPremium(
      paymentFrequency: deltaPremium?.paymentFrequency,
      toBePaid: deltaPremium?.adhocPayment,
      paidAmount: deltaPremium?.paidAmount,
      newAmount: deltaPremium?.newGrossPremium,
      previousInstallment: deltaPremium?.previousInstallment,
      newInstallment: deltaPremium?.newInstallment,
    );
  }
}
